import { useCallback, useEffect, useRef, useState } from 'react'
import { HtmlNode } from './lib/types'
import LeftPanel from './builderComponents/LeftPanel.tsx'
import RightPanel from './builderComponents/RightPanel.tsx'
import WelcomeScreen from './components/WelcomeScreen'
import TemplateSelectionScreen from './components/TemplateSelectionScreen'
// Import extracted builder modules
import {
    createElementClickHandler,
    handleKeyDown,
    makeElementDraggable,
    removeEventHandlers
} from './lib/builder/elementHandlers'
import { createTextEditHandler, isTextEditable } from './lib/builder/textEdit'
import { parseHtmlContent, updateIframeContent, updateIframeHeight } from './lib/builder/htmlParser'
import {
    clearCanvas,
    drawHoverOverlay,
    drawOverlay,
    getElementAtPosition,
    setupHoverOverlayEventListeners,
    setupOverlayEventListeners
} from './lib/builder/canvasOverlay'
import { handleDragLeave, handleDragOver, handleDrop } from './lib/builder/dragDrop'
import {
    clearSavedFile,
    createNewProject,
    loadSavedFileData,
    openProject,
    saveHtmlContent
} from './lib/builder/fileSystem'
import { Viewer } from './lib/builder/viewer'
import { CircleX, HardDriveDownload } from 'lucide-react'
import { Button } from '@/components/ui/button.tsx'
import { getElementNodePath } from '@/lib/utils.ts'
import { Tooltip, TooltipTrigger } from '@/components/ui/tooltip.tsx'

// LocalStorage utilities for new projects
const LOCALSTORAGE_KEY = 'website-builder-new-project'

const saveToLocalStorage = (content: string, filename: string): void => {
    const projectData = {
        content,
        filename,
        timestamp: Date.now()
    }
    localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(projectData))
}

const loadFromLocalStorage = (): {content: string; filename: string} | null => {
    try {
        const stored = localStorage.getItem(LOCALSTORAGE_KEY)
        if (stored) {
            const projectData = JSON.parse(stored)
            return {
                content: projectData.content,
                filename: projectData.filename
            }
        }
    } catch (error) {
        console.error('Error loading from localStorage:', error)
    }
    return null
}

const clearLocalStorage = (): void => {
    localStorage.removeItem(LOCALSTORAGE_KEY)
}

export default function App() {
    const [htmlContent, setHtmlContent] = useState<string>('')
    const [htmlStructure, setHtmlStructure] = useState<HtmlNode | null>(null)
    const [selectedElement, setSelectedElement] = useState<HTMLElement>()
    const [hoveredElement, setHoveredElement] = useState<HTMLElement>()
    const [filename, setFilename] = useState<string>('')
    const [currentScreen, setCurrentScreen] = useState<'welcome' | 'template-selection' | 'builder'>('welcome')
    const iframeRef = useRef<HTMLIFrameElement>(null)
    const fileHandleRef = useRef<FileSystemFileHandle | null>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const hoverCanvasRef = useRef<HTMLCanvasElement>(null)
    const dropIndicatorRef = useRef<HTMLDivElement>(null)
    const dragOverElemRef = useRef<HTMLElement | null>(null)
    const dragPosition = useRef<'above' | 'below' | 'inside'>('below')
    const viewerContainerRef = useRef<HTMLDivElement>(null)
    const iframeWrapperRef = useRef<HTMLDivElement>(null)
    const viewerRef = useRef<Viewer | null>(null)

    // Store event listener references for proper cleanup
    const eventListenersRef = useRef<Map<HTMLElement, {
        click: (e: MouseEvent) => void;
        dblclick?: (e: MouseEvent) => void;
        dragstart?: (e: DragEvent) => void;
        // mouseenter: (e: MouseEvent) => void;
        // mouseleave: (e: MouseEvent) => void;
    }>>(new Map())

    // Create element click handler using the extracted function
    const handleElementClick = useCallback((element: HTMLElement) => {
        return createElementClickHandler(element, (selectedElem) => {
            // When an element is selected, make it draggable
            const dragStartHandler = makeElementDraggable(selectedElem)

            // Store the dragstart handler for cleanup
            const existingHandlers = eventListenersRef.current.get(selectedElem) || {}
            // eslint-disable-next-line
            // @ts-ignore
            eventListenersRef.current.set(selectedElem, {
                ...existingHandlers,
                dragstart: dragStartHandler
            })

            // Add the dragstart event listener
            selectedElem.addEventListener('dragstart', dragStartHandler as EventListener)

            // Update the selected element state
            setSelectedElement(selectedElem)
        })
    }, [])

    // We're now using mousemove for hover detection instead of mouseenter/mouseleave

    // Function to draw the hover overlay using the extracted function
    const handleDrawHoverOverlay = useCallback((element?: HTMLElement) => {
        if (hoverCanvasRef.current && iframeRef.current) {
            const viewerState = viewerRef.current?.getState()
            drawHoverOverlay(element, hoverCanvasRef.current, iframeRef.current, viewerState)
        }
    }, [hoverCanvasRef, iframeRef])

    // Function to draw the overlay using the extracted function
    const handleDrawOverlay = useCallback((element: HTMLElement) => {
        if (canvasRef.current && iframeRef.current) {
            const viewerState = viewerRef.current?.getState()
            drawOverlay(element, canvasRef.current, iframeRef.current, viewerState)
        }
    }, [canvasRef, iframeRef])
    // We're now using the imported isFileSystemAccessSupported function

    // Functions to handle drag and drop of components using the extracted functions
    const handleDragOverWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDragOver(e, iframeRef.current, dragOverElemRef, dragPosition, dropIndicatorRef)
        }
    }, [iframeRef, dropIndicatorRef])

    // Function to save the current state of the iframe content back to the file using the extracted function
    const handleStyleUpdate = useCallback(async () => {
        // First, immediately redraw the canvas overlay when styles change

        const iframeDocument = iframeRef.current?.contentDocument

        if (!iframeRef.current || !iframeDocument) {
            console.warn('Cannot save: iframe not available.')
            return
        }

        // For new projects (template-based), save to localStorage
        if (fileHandleRef.current) {
            if (fileHandleRef.current) {
                saveHtmlContent(
                    iframeRef.current,
                    fileHandleRef.current,
                    () => console.log('File saved successfully!'),
                    (error) => console.error('Error saving file:', error),
                )
            } else {
                console.warn('Cannot save: no file handle available for opened project.')
            }
        } else {
            const updatedHtmlContent = iframeDocument.documentElement.outerHTML
            saveToLocalStorage(updatedHtmlContent, filename)
            console.log('New project saved to localStorage!')
        }
        if (selectedElement) {
            handleDrawOverlay(selectedElement)
        }
    }, [handleDrawOverlay, selectedElement, filename])

    // Create debounced save function with 500ms delay
    // const debouncedSaveTemplate = useCallback(
    //     debounce(handleStyleUpdate, 500),
    //     [handleStyleUpdate]
    // )

    // Function to handle HTML attribute changes
    const handleAttributeChange = useCallback(async () => {
        // The attribute change is already applied to the DOM element in PropertiesPanel
        // We just need to save the updated content
        await handleStyleUpdate()
    }, [handleStyleUpdate])

    const doubleClickHandler = useCallback((element: HTMLElement) => (event: MouseEvent) => {
        event.stopPropagation()
        event.preventDefault()

        const textEditHandler = createTextEditHandler(
            element,
            setHtmlContent,
            setSelectedElement,
            iframeRef.current as HTMLIFrameElement,
            () => {
                // Redraw overlay immediately for visual feedback
                // handleDrawOverlay(element)
                // Save template with debounce
                // debouncedSaveTemplate()
                handleStyleUpdate()
            }
        )
        textEditHandler(event)
        // } , [handleDrawOverlay, debouncedSaveTemplate])
    }, [handleDrawOverlay])

    // Reusable function to setup event handlers for elements
    const setupElementEventHandlers = useCallback((element: HTMLElement) => {
        const clickHandler = handleElementClick(element)

        // Create text edit handler for double-click if element is text-editable
        let dblclickHandler
        if (isTextEditable(element)) {
            dblclickHandler = doubleClickHandler(element)
        }

        // Store event handlers for later cleanup
        eventListenersRef.current.set(element, {
            click: clickHandler,
            dblclick: dblclickHandler,
        })

        // Attach event listeners using the stored handlers
        element.addEventListener('click', clickHandler)
        if (dblclickHandler) {
            element.addEventListener('dblclick', dblclickHandler)
        }
    }, [handleElementClick, doubleClickHandler])

    const handleDropWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDrop(
                e,
                iframeRef.current,
                dragOverElemRef,
                dragPosition,
                (content) => {
                    setHtmlContent(content)
                    handleStyleUpdate()
                },
                setupElementEventHandlers, // TODO remove
                dropIndicatorRef
            )
        }
    }, [setupElementEventHandlers])

    const handleDragLeaveWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDragLeave(e, iframeRef.current, dropIndicatorRef)
        }
    }, [iframeRef, dropIndicatorRef])

    // Function to handle DOM structure changes (like converting to link)
    const handleDomChange = useCallback(async () => {
        if (!iframeRef.current?.contentDocument) return

        // Get the updated HTML content from the iframe
        const updatedHtmlContent = iframeRef.current.contentDocument.documentElement.outerHTML

        // Update the HTML content state
        // setHtmlContent(updatedHtmlContent)

        // Parse the updated structuresss
        const newStructure = parseHtmlContent(updatedHtmlContent)
        // setHtmlStructure(newStructure)

        // Re-setup event handlers for all elements
        // if (selectedElement) {
        //     setupElementEventHandlers(selectedElement)
        // }

        // Save the changes
        // await handleStyleUpdate()
    }, [handleStyleUpdate, setupElementEventHandlers, selectedElement])
    // }, [handleStyleUpdate])

    // Load saved file handle on initial mount using the extracted function
    useEffect(() => {
        let hasLoadedFile = false

        // First, try to load opened file data from IndexedDB
        loadSavedFileData(
            (handle, content) => {
                if (handle) {
                    fileHandleRef.current = handle
                    if (content) {
                        setHtmlContent(content)
                        setCurrentScreen('builder')
                        hasLoadedFile = true
                    }
                }

                // If no opened project found, try to load new project from localStorage
                if (!hasLoadedFile) {
                    const localStorageData = loadFromLocalStorage()
                    if (localStorageData) {
                        setFilename(localStorageData.filename)
                        setHtmlContent(localStorageData.content)
                        setCurrentScreen('builder')
                    }
                }
            },
            (error) => {
                console.error('Error loading saved file:', error)
                fileHandleRef.current = null

                // Still try to load from localStorage if file loading failed
                const localStorageData = loadFromLocalStorage()
                if (localStorageData) {
                    setFilename(localStorageData.filename)
                    setHtmlContent(localStorageData.content)
                    setCurrentScreen('builder')
                }
            }
        )
    }, [])

    // Open project using the extracted function
    const handleOpenProject = async () => {
        openProject(
            (filename, content, handle) => {
                setFilename(filename)
                fileHandleRef.current = handle
                setHtmlContent(content)
                setCurrentScreen('builder')
                // Clear any localStorage data since we're now working with a file
                clearLocalStorage()
            },
            (error) => {
                console.log('File picking was canceled or failed:', error)
            }
        )
    }

    // Show template selection screen
    const handleShowTemplateSelection = () => {
        setCurrentScreen('template-selection')
    }

    // Go back to welcome screen
    const handleBackToWelcome = () => {
        setCurrentScreen('welcome')
    }

    // Create new project with selected template
    const handleTemplateSelect = async (templateType: 'blank' | 'personal' | 'business' | 'shop') => {
        createNewProject(
            templateType,
            (filename, content) => {
                setFilename(filename)
                fileHandleRef.current = null // No file handle for new projects until saved
                setHtmlContent(content)
                setCurrentScreen('builder')
                // Save immediately to localStorage
                saveToLocalStorage(content, filename)
            },
            (error) => {
                console.error('Error creating new project:', error)
            }
        )
    }

    // Clear saved file using the extracted function
    const handleClearSavedFile = async () => {
        clearSavedFile(() => {
            setHtmlContent('')
            setFilename('')
            setCurrentScreen('welcome')
            setHtmlStructure(null)
            setSelectedElement(undefined)
            fileHandleRef.current = null
            // Also clear localStorage
            clearLocalStorage()
        })
    }

    // Download project as HTML file
    const handleDownloadProject = useCallback(() => {
        if (!iframeRef.current) {
            console.warn('Cannot download: iframe not available.')
            return
        }

        const iframeDocument = iframeRef.current.contentDocument
        if (!iframeDocument) {
            console.warn('Cannot download: iframe document not available.')
            return
        }

        // Get the current HTML content from the iframe
        const htmlContent = iframeDocument.documentElement.outerHTML

        // Create a blob with the HTML content
        const blob = new Blob([htmlContent], {type: 'text/html'})
        const url = URL.createObjectURL(blob)

        // Create a temporary download link
        const downloadLink = document.createElement('a')
        downloadLink.href = url
        downloadLink.download = filename || 'website.html'

        // Append to body, click, and remove
        document.body.appendChild(downloadLink)
        downloadLink.click()
        document.body.removeChild(downloadLink)

        // Clean up the URL
        URL.revokeObjectURL(url)

        console.log('Project downloaded successfully!')
    }, [filename])

    // Parse HTML structure into a tree using the extracted function
    useEffect(() => {
        const htmlNode = parseHtmlContent(htmlContent)
        setHtmlStructure(htmlNode)
    }, [htmlContent])

    // Update iframe content when htmlContent changes and set up drag-and-drop using extracted functions
    useEffect(() => {
        if (!htmlContent || !iframeRef.current) return

        // Get iframe document
        const iframe = iframeRef.current
        const iframeWindow = iframe.contentWindow

        // Clear all existing event listeners from previous renders
        removeEventHandlers(eventListenersRef.current)

        // Update iframe content
        const iframeDocument = updateIframeContent(iframe, htmlContent)
        if (!iframeDocument) return

        const handleIframeMouseMove = (e: MouseEvent) => {
            // Get the element at the current mouse position
            const element = getElementAtPosition(e.clientX, e.clientY, iframe)

            // console.log('eee ->', element, hoveredElement)
            // Update the hovered element if it changed
            if (element !== hoveredElement) {
                setHoveredElement(element)
            }
        }

        // Add event handlers to elements in iframe
        const setupElementHandlers = () => {

            // const allElements = iframeDocument.querySelectorAll('*')
            // allElements.forEach(el => {
            //     // Check if element is an HTMLElement
            //     if (el instanceof (iframeWindow as any).HTMLElement) {
            //         const htmlEl = el as HTMLElement
            //         // Use the reusable setupElementEventHandlers function
            //         setupElementEventHandlers(htmlEl)// TODO remove
            //     }
            // })

            // Add mousemove event listener to the iframe document for hover detection
            iframeDocument.addEventListener('mousemove', handleIframeMouseMove as EventListener)

            // Add dragover, drop, and dragleave event listeners to the iframe document
            iframeDocument.addEventListener('dragover', handleDragOverWrapper as EventListener)
            iframeDocument.addEventListener('drop', handleDropWrapper as EventListener)
            iframeDocument.addEventListener('dragleave', handleDragLeaveWrapper as EventListener)

            iframeDocument.addEventListener('click', (event) => {
                event.stopPropagation()
                event.preventDefault()

                const element = event.target as HTMLElement

                return handleElementClick(element)(event)
            })
            iframeDocument.addEventListener('dblclick', (event) => {
                event.stopPropagation()
                event.preventDefault()

                const element = event.target as HTMLElement

                let dblclickHandler
                if (isTextEditable(element)) {
                    dblclickHandler = doubleClickHandler(element)
                }

                return dblclickHandler?.(event)
            })
        }

        // Allow iframe content to render first
        setTimeout(setupElementHandlers, 100)

        // Update iframe height
        updateIframeHeight(iframe)

        // Cleanup event listeners when component unmounts or content changes
        return () => {
            removeEventHandlers(eventListenersRef.current)

            if (iframeDocument) {
                iframeDocument.removeEventListener('mousemove', handleIframeMouseMove as EventListener)
                iframeDocument.removeEventListener('dragover', handleDragOverWrapper as EventListener)
                iframeDocument.removeEventListener('drop', handleDropWrapper as EventListener)
                iframeDocument.removeEventListener('dragleave', handleDragLeaveWrapper as EventListener)
            }

        }
    }, [htmlContent, handleDragOverWrapper, handleDropWrapper, handleDragLeaveWrapper, setHtmlContent, setSelectedElement, setupElementEventHandlers])

    // Effect to draw margins on canvas when element is selected using the extracted function
    useEffect(() => {

        // If no element is selected, or refs aren't ready, just return early
        if (!iframeRef.current || !canvasRef.current) {
            return

        }
        if (!selectedElement) {
            if (canvasRef.current) {
                clearCanvas(canvasRef.current)
            }
            if (hoverCanvasRef.current) {
                clearCanvas(hoverCanvasRef.current)
            }
            return
        }

        // Draw the overlay initially when the selected element changes
        handleDrawOverlay(selectedElement)

        // Setup overlay event listeners using the extracted function
        const cleanup = setupOverlayEventListeners(
            selectedElement,
            canvasRef.current,
            iframeRef.current,
            () => viewerRef.current?.getState()
        )

        // Return cleanup function
        return cleanup
    }, [selectedElement, handleDrawOverlay, setupElementEventHandlers])

    // Effect to draw hover overlay on canvas when element is hovered
    useEffect(() => {
        // Draw the hover overlay when the hovered element changes
        handleDrawHoverOverlay(hoveredElement)

        // If no element is hovered, or refs aren't ready, just return early
        if (!hoveredElement || !iframeRef.current || !hoverCanvasRef.current) {
            return
        }

        // Setup hover overlay event listeners
        const cleanup = setupHoverOverlayEventListeners(
            hoveredElement,
            hoverCanvasRef.current,
            iframeRef.current,
            () => viewerRef.current?.getState()
        )

        // Return cleanup function
        return cleanup
    }, [hoveredElement, handleDrawHoverOverlay, setupElementEventHandlers])

    // Handle keydown events for element deletion and copy-paste using the extracted function
    const handleKeyDownWrapper = useCallback((e: KeyboardEvent) => {
        handleKeyDown(
            e,
            selectedElement,
            iframeRef,
            setHtmlContent,
            setSelectedElement,
            handleStyleUpdate,
            setupElementEventHandlers // todo remove
        )
    }, [handleStyleUpdate, selectedElement, setupElementEventHandlers])

    // Add keydown event listener
    useEffect(() => {
        const iframe = iframeRef.current
        if (!iframe || !iframe.contentDocument) return

        const iframeDocument = iframe.contentDocument
        iframeDocument.addEventListener('keydown', handleKeyDownWrapper)

        return () => {
            iframeDocument.removeEventListener('keydown', handleKeyDownWrapper)
        }
    }, [handleKeyDownWrapper, iframeRef, htmlContent])

    // Initialize viewer for panning and zooming
    useEffect(() => {
        if (currentScreen === 'builder' && viewerContainerRef.current && iframeWrapperRef.current) {
            // Create viewer instance
            viewerRef.current = new Viewer(
                viewerContainerRef.current,
                iframeWrapperRef.current,
                iframeRef.current ?? undefined,
            )

            // Center and zoom out slightly on initial load
            // Use setTimeout to ensure iframe content is loaded
            const centerOnLoad = () => {
                if (viewerRef.current && iframeRef.current?.offsetWidth) {
                    viewerRef.current.centerAndZoomOut(0.9) // 90% zoom out for a bit of breathing room
                }
            }

            // Try centering immediately and also after a delay
            centerOnLoad()

            return () => {
                // Cleanup viewer on unmount or screen change
                if (viewerRef.current) {
                    viewerRef.current.destroy()
                    viewerRef.current = null
                }
            }
        }
    }, [currentScreen])

    return (
        <div className="flex h-screen w-full">
            {currentScreen === 'welcome' && (
                <WelcomeScreen
                    onCreateNewProject={handleShowTemplateSelection}
                    onOpenProject={handleOpenProject}
                />
            )}

            {currentScreen === 'template-selection' && (
                <TemplateSelectionScreen
                    onTemplateSelect={handleTemplateSelect}
                    onBack={handleBackToWelcome}
                />
            )}

            {currentScreen === 'builder' && htmlContent && (
                <div className="flex flex-col h-full w-full">
                    <div className="border-b border-secondary py-1 px-3 flex flex-row items-center gap-2">
                        <TooltipTrigger>
                            <Button
                                onClick={handleClearSavedFile}
                                size="icon"
                                variant="ghost"
                            >
                                <CircleX/>
                            </Button>
                            <Tooltip>Close project</Tooltip>
                        </TooltipTrigger>
                        <TooltipTrigger>
                            <Button
                                size="icon"
                                variant="ghost"
                                onClick={handleDownloadProject}
                            >
                                <HardDriveDownload/>
                            </Button>
                            <Tooltip>Download project files</Tooltip>
                        </TooltipTrigger>

                    </div>
                    <div className="flex w-full flex-1 basis-auto overflow-hidden">

                        {/* Left Panel - HTML Structure */}
                        <div className="w-[250px] h-full overflow-y-auto flex-shrink-0">
                            <LeftPanel
                                htmlStructure={htmlStructure}
                                selectedElement={selectedElement}
                                selectedElementPath={selectedElement ? getElementNodePath(selectedElement) : undefined}
                                onElementSelect={(nodeId) => {
                                    // Find the element in the iframe document based on the node structure
                                    if (iframeRef.current && iframeRef.current.contentDocument && htmlStructure) {
                                        const iframe = iframeRef.current
                                        const iframeDocument = iframe.contentDocument

                                        // Find the node in the htmlStructure tree by ID
                                        const findNodeById = (node: HtmlNode | null, id: string): HtmlNode | null => {
                                            if (!node) return null
                                            if (node.id === id) return node

                                            for(const child of node.children) {
                                                const found = findNodeById(child, id)
                                                if (found) return found
                                            }

                                            return null
                                        }

                                        // Find the node in the tree
                                        const node = findNodeById(htmlStructure, nodeId)
                                        if (node) {
                                            // Get the path to this node (tag names and indices)
                                            const getNodePath = (targetNode: HtmlNode, currentNode: HtmlNode | null = htmlStructure, path: number[] = []): number[] | null => {
                                                if (!currentNode) return null
                                                if (currentNode.id === targetNode.id) return path

                                                for(let i = 0; i < currentNode.children.length; i++) {
                                                    const childPath = getNodePath(targetNode, currentNode.children[i], [...path, i])
                                                    if (childPath) return childPath
                                                }

                                                return null
                                            }

                                            const nodePath = getNodePath(node)
                                            if (nodePath && iframeDocument) {
                                                // Use the path to find the corresponding element in the iframe
                                                let element: Element = iframeDocument.body

                                                // Follow the path through the DOM
                                                for(const index of nodePath) {
                                                    if (element.children[index]) {
                                                        element = element.children[index]
                                                    } else {
                                                        console.error('Element not found at index', index)
                                                        return
                                                    }
                                                }

                                                // Simulate a click on the element to select it
                                                const iframeWindow = iframe.contentWindow
                                                if (element instanceof (iframeWindow as any).HTMLElement) {
                                                    // eslint-disable-next-line
                                                    // @ts-ignore
                                                    setSelectedElement(element)

                                                    // Focus on the selected element using the viewer
                                                    if (viewerRef.current) {
                                                        // Small delay to ensure the element is properly selected first
                                                        setTimeout(() => {
                                                            viewerRef.current?.focusOnElement(element as HTMLElement, 100)
                                                        }, 100)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }}
                            />
                        </div>

                        {/* Center Panel - Rendered HTML using iframe */}
                        <div className="flex-1 h-full bg-border/10 relative overflow-hidden" style={{
                            backgroundSize: '20px 20px',
                            backgroundImage: 'linear-gradient(to right, hsl(var(--border)/0.3) 1px, transparent 1px), linear-gradient(to bottom, hsl(var(--border)/0.3) 1px, transparent 1px)'
                        }}
                             ref={viewerContainerRef}
                             onClick={() => {
                                 setSelectedElement(undefined)
                             }}
                             onMouseOver={() => {
                                 setHoveredElement(undefined)
                             }}
                        >
                            {/*<div ref={viewerContainerRef} className="absolute top-0 left-0 w-full h-full z-10 pointer-events-none"></div>*/}

                            <div
                                ref={iframeWrapperRef}
                                className="inline-block relative"
                                style={{
                                    transformOrigin: 'top left',
                                    // transition: 'transform 0.1s ease-out',
                                    minWidth: '100%',
                                    minHeight: '100%'
                                }}

                            >

                                <iframe
                                    ref={iframeRef}
                                    className="bg-background w-full h-full border-none min-h-screen"
                                    title="HTML Preview"
                                    sandbox="allow-same-origin allow-scripts"
                                    style={{overflow: 'hidden'}}
                                />
                                {/* Canvas for selected element visualization */}
                                <canvas ref={canvasRef} style={{display: 'none'}}/>
                                {/* Canvas for hovered element visualization */}
                                <canvas ref={hoverCanvasRef} className="absolute pointer-events-none z-10 outline outline-primary border border-secondary" style={{display: 'none'}}/>
                                {/* Drop indicator for drag and drop */}
                                <div
                                    ref={dropIndicatorRef}
                                    className="bg-primary shadow-sm"
                                    style={{
                                        display: 'none',
                                        position: 'absolute',
                                        transition: 'all 0.2s ease',
                                        pointerEvents: 'none',
                                        zIndex: 1000
                                    }}
                                />
                            </div>
                            {/*</div>*/}
                        </div>

                        {/* Right Panel - Element Properties */}
                        <div className="w-[250px] h-full ">
                            <RightPanel
                                selectedElement={selectedElement}
                                onStyleChange={handleStyleUpdate}
                                onAttributeChange={handleAttributeChange}
                                onDomChange={handleDomChange}
                            />
                        </div>

                    </div>
                </div>
            )}
        </div>
    )
}
