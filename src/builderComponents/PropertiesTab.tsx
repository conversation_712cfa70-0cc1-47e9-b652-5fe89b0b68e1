import { Jolly<PERSON>ext<PERSON>ield } from '@/components/ui/textfield'
import { Button } from '@/components/ui/button'

interface PropertiesTabProps {
    selectedElement?: HTMLElement;
    htmlAttributes: {
        id: string;
        href: string;
    };
    handleAttributeChange: (attribute: string, value: string) => void;
    onDomChange?: () => void;
}

const PropertiesTab = ({
    selectedElement,
    htmlAttributes,
    handleAttributeChange,
    onDomChange
}: PropertiesTabProps) => {
    // Check if the selected element is a link
    const isLink = selectedElement?.tagName.toLowerCase() === 'a'

    // Function to convert element to a link
    const convertToLink = () => {
        if (!selectedElement || isLink) return

        // Create a new anchor element
        const linkElement = document.createElement('a')
        linkElement.href = '#'

        // Copy all attributes from the original element to the link
        Array.from(selectedElement.attributes).forEach(attr => {
            linkElement.setAttribute(attr.name, attr.value)
        })

        // Copy all styles from the original element to the link
        // const computedStyles = window.getComputedStyle(selectedElement)
        // Array.from(computedStyles).forEach(property => {
        //     linkElement.style.setProperty(property, computedStyles.getPropertyValue(property))
        // })

        // Move all children from the original element to the link
        while (selectedElement.firstChild) {
            linkElement.appendChild(selectedElement.firstChild)
        }

        // Replace the original element with the link
        selectedElement.parentNode?.replaceChild(linkElement, selectedElement)

        // Notify parent about DOM change
        onDomChange?.()
    }

    // Function to handle href changes
    const handleHrefChange = (value: string) => {
        if (!selectedElement || !isLink) return

        handleAttributeChange('href', value)
    }

    return (
        <div className="mt-4 flex flex-col gap-3">
            <JollyTextField
                label="ID"
                aria-label="Element ID"
                value={htmlAttributes.id}
                onChange={(value) => handleAttributeChange('id', value)}
                placeholder="Enter element ID"
            />

            {/* Link functionality */}
            {isLink ? (
                <JollyTextField
                    label="URL"
                    aria-label="Link URL"
                    value={htmlAttributes.href}
                    onChange={handleHrefChange}
                    placeholder="Enter URL (e.g., https://example.com)"
                />
            ) : (
                <Button
                    variant="outline"
                    size="sm"
                    onPress={convertToLink}
                >
                    Convert to a Link
                </Button>
            )}
        </div>
    )
}

export default PropertiesTab
