import { useCallback, useEffect, useRef } from 'react'
import { HtmlNode } from '../lib/types'
import {
    createElementClickHandler,
    makeElementDraggable,
    removeEventHandlers
} from '../lib/builder/elementHandlers'
import { createTextEditHandler, isTextEditable } from '../lib/builder/textEdit'
import { parseHtmlContent, updateIframeContent, updateIframeHeight } from '../lib/builder/htmlParser'
import {
    clearCanvas,
    drawHoverOverlay,
    drawOverlay,
    getElementAtPosition,
    setupHoverOverlayEventListeners,
    setupOverlayEventListeners
} from '../lib/builder/canvasOverlay'
import { handleDragLeave, handleDragOver, handleDrop } from '../lib/builder/dragDrop'
import { Viewer } from '../lib/builder/viewer'

interface ViewerContainerProps {
    htmlContent: string
    selectedElement?: HTMLElement
    hoveredElement?: HTMLElement
    htmlStructure: HtmlNode | null
    onElementSelect: (element: HTMLElement) => void
    onHoveredElementChange: (element?: HTMLElement) => void
    onStyleUpdate: () => void
    onHtmlContentChange: (content: string) => void
}

export default function ViewerContainer({
    htmlContent,
    selectedElement,
    hoveredElement,
    htmlStructure,
    onElementSelect,
    onHoveredElementChange,
    onStyleUpdate,
    onHtmlContentChange
}: ViewerContainerProps) {
    const iframeRef = useRef<HTMLIFrameElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const hoverCanvasRef = useRef<HTMLCanvasElement>(null)
    const dropIndicatorRef = useRef<HTMLDivElement>(null)
    const dragOverElemRef = useRef<HTMLElement | null>(null)
    const dragPosition = useRef<'above' | 'below' | 'inside'>('below')
    const viewerContainerRef = useRef<HTMLDivElement>(null)
    const iframeWrapperRef = useRef<HTMLDivElement>(null)
    const viewerRef = useRef<Viewer | null>(null)

    // Store event listener references for proper cleanup
    const eventListenersRef = useRef<Map<HTMLElement, {
        click: (e: MouseEvent) => void;
        dblclick?: (e: MouseEvent) => void;
        dragstart?: (e: DragEvent) => void;
    }>>(new Map())

    // Create element click handler using the extracted function
    const handleElementClick = useCallback((element: HTMLElement) => {
        return createElementClickHandler(element, (selectedElem) => {
            // When an element is selected, make it draggable
            const dragStartHandler = makeElementDraggable(selectedElem)

            // Add the dragstart event listener
            selectedElem.addEventListener('dragstart', dragStartHandler as EventListener)

            // Update the selected element state
            onElementSelect(selectedElem)
        })
    }, [onElementSelect])

    // Function to draw the hover overlay using the extracted function
    const handleDrawHoverOverlay = useCallback((element?: HTMLElement) => {
        if (hoverCanvasRef.current && iframeRef.current) {
            const viewerState = viewerRef.current?.getState()
            drawHoverOverlay(element, hoverCanvasRef.current, iframeRef.current, viewerState)
        }
    }, [])

    // Function to draw the overlay using the extracted function
    const handleDrawOverlay = useCallback((element: HTMLElement) => {
        if (canvasRef.current && iframeRef.current) {
            const viewerState = viewerRef.current?.getState()
            drawOverlay(element, canvasRef.current, iframeRef.current, viewerState)
        }
    }, [])

    // Functions to handle drag and drop of components using the extracted functions
    const handleDragOverWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDragOver(e, iframeRef.current, dragOverElemRef, dragPosition, dropIndicatorRef)
        }
    }, [])

    const handleDropWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDrop(
                e,
                iframeRef.current,
                dragOverElemRef,
                dragPosition,
                (content) => {
                    onHtmlContentChange(content)
                    onStyleUpdate()
                },
                dropIndicatorRef
            )
        }
    }, [onHtmlContentChange, onStyleUpdate])

    const handleDragLeaveWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDragLeave(e, iframeRef.current, dropIndicatorRef)
        }
    }, [])

    const doubleClickHandler = useCallback((element: HTMLElement) => (event: MouseEvent) => {
        event.stopPropagation()
        event.preventDefault()

        const textEditHandler = createTextEditHandler(
            element,
            onHtmlContentChange,
            onElementSelect,
            iframeRef.current as HTMLIFrameElement,
            () => {
                onStyleUpdate()
            }
        )
        textEditHandler(event)
    }, [onHtmlContentChange, onElementSelect, onStyleUpdate])

    // Update iframe content when htmlContent changes and set up drag-and-drop using extracted functions
    useEffect(() => {
        if (!htmlContent || !iframeRef.current) return

        // Get iframe document
        const iframe = iframeRef.current

        // Clear all existing event listeners from previous renders
        removeEventHandlers(eventListenersRef.current)

        // Update iframe content
        const iframeDocument = updateIframeContent(iframe, htmlContent)
        if (!iframeDocument) return

        const handleIframeMouseMove = (e: MouseEvent) => {
            // Get the element at the current mouse position
            const element = getElementAtPosition(e.clientX, e.clientY, iframe)

            // Update the hovered element if it changed
            if (element !== hoveredElement) {
                onHoveredElementChange(element)
            }
        }

        // Add event handlers to elements in iframe
        const setupElementHandlers = () => {
            // Add mousemove event listener to the iframe document for hover detection
            iframeDocument.addEventListener('mousemove', handleIframeMouseMove as EventListener)

            // Add dragover, drop, and dragleave event listeners to the iframe document
            iframeDocument.addEventListener('dragover', handleDragOverWrapper as EventListener)
            iframeDocument.addEventListener('drop', handleDropWrapper as EventListener)
            iframeDocument.addEventListener('dragleave', handleDragLeaveWrapper as EventListener)

            iframeDocument.addEventListener('click', (event) => {
                event.stopPropagation()
                event.preventDefault()

                const element = event.target as HTMLElement
                return handleElementClick(element)(event)
            })

            iframeDocument.addEventListener('dblclick', (event) => {
                event.stopPropagation()
                event.preventDefault()

                const element = event.target as HTMLElement

                let dblclickHandler
                if (isTextEditable(element)) {
                    dblclickHandler = doubleClickHandler(element)
                }

                return dblclickHandler?.(event)
            })
        }

        // Allow iframe content to render first
        setTimeout(setupElementHandlers, 100)

        // Update iframe height
        updateIframeHeight(iframe)

        // Cleanup event listeners when component unmounts or content changes
        return () => {
            removeEventHandlers(eventListenersRef.current)

            if (iframeDocument) {
                iframeDocument.removeEventListener('mousemove', handleIframeMouseMove as EventListener)
                iframeDocument.removeEventListener('dragover', handleDragOverWrapper as EventListener)
                iframeDocument.removeEventListener('drop', handleDropWrapper as EventListener)
                iframeDocument.removeEventListener('dragleave', handleDragLeaveWrapper as EventListener)
            }
        }
    }, [htmlContent, handleDragOverWrapper, handleDropWrapper, handleDragLeaveWrapper, hoveredElement, onHoveredElementChange, handleElementClick, doubleClickHandler])

    // Effect to draw margins on canvas when element is selected using the extracted function
    useEffect(() => {
        // If no element is selected, or refs aren't ready, just return early
        if (!iframeRef.current || !canvasRef.current) {
            return
        }
        if (!selectedElement) {
            if (canvasRef.current) {
                clearCanvas(canvasRef.current)
            }
            if (hoverCanvasRef.current) {
                clearCanvas(hoverCanvasRef.current)
            }
            return
        }

        // Draw the overlay initially when the selected element changes
        handleDrawOverlay(selectedElement)

        // Setup overlay event listeners using the extracted function
        const cleanup = setupOverlayEventListeners(
            selectedElement,
            canvasRef.current,
            iframeRef.current,
            () => viewerRef.current?.getState()
        )

        // Return cleanup function
        return cleanup
    }, [selectedElement, handleDrawOverlay])

    // Effect to draw hover overlay on canvas when element is hovered
    useEffect(() => {
        // Draw the hover overlay when the hovered element changes
        handleDrawHoverOverlay(hoveredElement)

        // If no element is hovered, or refs aren't ready, just return early
        if (!hoveredElement || !iframeRef.current || !hoverCanvasRef.current) {
            return
        }

        // Setup hover overlay event listeners
        const cleanup = setupHoverOverlayEventListeners(
            hoveredElement,
            hoverCanvasRef.current,
            iframeRef.current,
            () => viewerRef.current?.getState()
        )

        // Return cleanup function
        return cleanup
    }, [hoveredElement, handleDrawHoverOverlay])

    // Initialize viewer for panning and zooming
    useEffect(() => {
        if (viewerContainerRef.current && iframeWrapperRef.current) {
            // Create viewer instance
            viewerRef.current = new Viewer(
                viewerContainerRef.current,
                iframeWrapperRef.current,
                iframeRef.current ?? undefined,
            )

            // Center and zoom out slightly on initial load
            const centerOnLoad = () => {
                if (viewerRef.current && iframeRef.current?.offsetWidth) {
                    viewerRef.current.centerAndZoomOut(0.9) // 90% zoom out for a bit of breathing room
                }
            }

            // Try centering immediately and also after a delay
            centerOnLoad()

            return () => {
                // Cleanup viewer on unmount or screen change
                if (viewerRef.current) {
                    viewerRef.current.destroy()
                    viewerRef.current = null
                }
            }
        }
    }, [])

    // Expose methods for parent component to interact with viewer
    const focusOnElement = useCallback((element: HTMLElement, padding?: number) => {
        if (viewerRef.current) {
            setTimeout(() => {
                viewerRef.current?.focusOnElement(element, padding)
            }, 100)
        }
    }, [])

    // Expose the focusOnElement method to parent via ref (if needed)
    // This could be done with useImperativeHandle if the parent needs direct access

    return (
        <div className="flex-1 h-full bg-border/10 relative overflow-hidden" style={{
            backgroundSize: '20px 20px',
            backgroundImage: 'linear-gradient(to right, hsl(var(--border)/0.3) 1px, transparent 1px), linear-gradient(to bottom, hsl(var(--border)/0.3) 1px, transparent 1px)'
        }}
             ref={viewerContainerRef}
             onClick={() => {
                 onElementSelect(undefined as any)
             }}
             onMouseOver={() => {
                 onHoveredElementChange(undefined)
             }}
        >
            <div
                ref={iframeWrapperRef}
                className="inline-block relative"
                style={{
                    transformOrigin: 'top left',
                    minWidth: '100%',
                    minHeight: '100%'
                }}
            >
                <iframe
                    ref={iframeRef}
                    className="bg-background w-full h-full border-none min-h-screen"
                    title="HTML Preview"
                    sandbox="allow-same-origin allow-scripts"
                    style={{overflow: 'hidden'}}
                />
                {/* Canvas for selected element visualization */}
                <canvas ref={canvasRef} style={{display: 'none'}}/>
                {/* Canvas for hovered element visualization */}
                <canvas ref={hoverCanvasRef} className="absolute pointer-events-none z-10 outline outline-primary border border-secondary" style={{display: 'none'}}/>
                {/* Drop indicator for drag and drop */}
                <div
                    ref={dropIndicatorRef}
                    className="bg-primary shadow-sm"
                    style={{
                        display: 'none',
                        position: 'absolute',
                        transition: 'all 0.2s ease',
                        pointerEvents: 'none',
                        zIndex: 1000
                    }}
                />
            </div>
        </div>
    )
}

// Export the focusOnElement method for external use if needed
export type ViewerContainerRef = {
    focusOnElement: (element: HTMLElement, padding?: number) => void
}
